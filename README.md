# Sistema de Controle de Entrada e Saída - Laboratório

Sistema web desenvolvido em PHP para gerenciar o controle de entrada e saída de equipamentos do laboratório de informática, utilizando uma planilha Excel como banco de dados.

## 📋 Funcionalidades

- ✅ **CRUD Completo**: Criar, Ler, Atualizar e Deletar registros
- 🔍 **Busca Avançada**: Pesquisar por empresa, solicitante, máquina, código do chamado
- 📊 **Filtros**: Filtrar por status (Em Andamento/Concluído)
- 📱 **Interface Responsiva**: Funciona em desktop, tablet e mobile
- 📈 **Dashboard**: Visualização clara dos dados em tabela
- 💾 **Backup Automático**: Trabalha diretamente com arquivo Excel existente

## 🛠️ Tecnologias Utilizadas

- **Backend**: PHP 7.4+
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Biblioteca**: PhpSpreadsheet (manipulação de Excel)
- **Servidor**: Apache (XAMPP)
- **Banco de Dados**: Arquivo Excel (.xlsx)

## 📁 Estrutura do Projeto

```
EntradaSaidaLab/
├── api/
│   └── index.php              # API REST endpoints
├── assets/
│   ├── css/
│   │   └── style.css          # Estilos CSS
│   └── js/
│       └── app.js             # JavaScript da aplicação
├── config/
│   └── config.php             # Configurações do sistema
├── src/
│   └── ExcelManager.php       # Classe para manipular Excel
├── vendor/                    # Dependências do Composer
├── .htaccess                  # Configurações Apache
├── composer.json              # Dependências PHP
├── index.html                 # Interface principal
├── test.php                   # Arquivo de teste
└── README.md                  # Este arquivo
```

## 🚀 Instalação

### Pré-requisitos

1. **XAMPP** instalado e funcionando
2. **PHP 7.4+** com extensões:
   - zip
   - xml
   - gd
   - mbstring
3. **Composer** instalado

### Passos de Instalação

1. **Clone/Copie o projeto** para a pasta do XAMPP:
   ```
   c:\xampp\htdocs\EntradaSaidaLab\
   ```

2. **Instale as dependências** via Composer:
   ```bash
   cd c:\xampp\htdocs\EntradaSaidaLab
   composer install
   ```

3. **Configure o caminho da planilha** no arquivo `config/config.php`:
   ```php
   define('EXCEL_FILE_PATH', '\\\\************\\dados\\OneDrive\\1-SUPORTE\\Controles\\Lab\\Controle Entradas e Saidas.xlsx');
   ```

4. **Inicie o XAMPP** (Apache)

5. **Teste a instalação**:
   - Acesse: `http://localhost/EntradaSaidaLab/test.php`
   - Verifique se todos os testes passaram

6. **Acesse o sistema**:
   - URL: `http://localhost/EntradaSaidaLab/`

## 📊 Estrutura da Planilha Excel

A planilha deve ter as seguintes colunas (na ordem):

| Coluna | Campo | Tipo | Obrigatório |
|--------|-------|------|-------------|
| A | Empresa | Texto | Sim |
| B | Ordem De Serviço | Texto | Sim |
| C | Solicitante | Texto | Sim |
| D | Máquina | Texto | Sim |
| E | Código do chamado | Texto | Sim |
| F | Data de entrada | Data | Sim |
| G | Data de saida | Data | Não |
| H | Descrição do serviço | Texto | Sim |

## 🔧 API Endpoints

### Registros
- `GET /api/records` - Listar todos os registros
- `GET /api/records/{id}` - Buscar registro específico
- `POST /api/records` - Criar novo registro
- `PUT /api/records/{id}` - Atualizar registro
- `DELETE /api/records/{id}` - Deletar registro

### Busca
- `GET /api/search?q={termo}` - Buscar registros

## 💡 Como Usar

### Adicionar Novo Registro
1. Preencha o formulário na parte superior da página
2. Clique em "Salvar"
3. O registro será adicionado à planilha Excel

### Editar Registro
1. Clique no botão "Editar" na linha desejada
2. O formulário será preenchido com os dados atuais
3. Modifique os campos necessários
4. Clique em "Salvar"

### Excluir Registro
1. Clique no botão "Excluir" na linha desejada
2. Confirme a exclusão
3. O registro será removido da planilha

### Buscar Registros
1. Digite o termo de busca no campo "Buscar"
2. A busca é feita automaticamente em tempo real
3. Busca nos campos: empresa, solicitante, máquina, código do chamado, descrição

### Filtrar por Status
1. Use o filtro "Status" para mostrar apenas:
   - **Em Andamento**: registros sem data de saída
   - **Concluído**: registros com data de saída preenchida

## 🔒 Segurança

- Arquivos PHP protegidos via `.htaccess`
- Validação de dados no frontend e backend
- Sanitização de entradas
- Controle de acesso aos diretórios sensíveis

## 🐛 Solução de Problemas

### Erro: "Arquivo Excel não encontrado"
- Verifique se o caminho no `config/config.php` está correto
- Verifique se o arquivo existe no servidor de rede
- Verifique permissões de acesso ao arquivo

### Erro: "Arquivo Excel não pode ser lido"
- Verifique permissões de leitura/escrita
- Verifique se o arquivo não está aberto em outro programa
- Verifique conectividade com o servidor de rede

### Interface não carrega
- Verifique se o Apache está rodando
- Verifique se o mod_rewrite está habilitado
- Verifique logs de erro do Apache

### API não funciona
- Verifique se o arquivo `.htaccess` existe
- Verifique se o mod_rewrite está habilitado no Apache
- Verifique logs de erro PHP

## 📞 Suporte

Para suporte técnico ou dúvidas sobre o sistema, consulte:
1. Arquivo `test.php` para diagnósticos
2. Logs de erro do Apache/PHP
3. Console do navegador (F12) para erros JavaScript

## 📝 Changelog

### v1.0.0
- Sistema CRUD completo
- Interface responsiva
- API REST
- Integração com Excel
- Sistema de busca e filtros
