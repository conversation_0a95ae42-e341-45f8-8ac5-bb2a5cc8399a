// Configurações da aplicação
const API_BASE_URL = 'api';
let currentEditingId = null;

// Elementos DOM
const form = document.getElementById('equipmentForm');
const tableBody = document.getElementById('recordsTableBody');
const searchInput = document.getElementById('searchInput');
const statusFilter = document.getElementById('statusFilter');
const recordCount = document.getElementById('recordCount');
const loadingOverlay = document.getElementById('loadingOverlay');
const formTitle = document.getElementById('form-title');

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadRecords();
    
    form.addEventListener('submit', handleFormSubmit);
    searchInput.addEventListener('input', debounce(handleSearch, 300));
    statusFilter.addEventListener('change', filterRecords);
});

// Função para mostrar/esconder loading
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Função para fazer requisições à API
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Erro na requisição');
        }
        
        return data;
    } catch (error) {
        console.error('Erro na API:', error);
        showNotification('Erro: ' + error.message, 'error');
        throw error;
    }
}

// Carregar todos os registros
async function loadRecords() {
    try {
        showLoading();
        const response = await apiRequest('records');
        displayRecords(response.records);
        updateRecordCount(response.total);
    } catch (error) {
        console.error('Erro ao carregar registros:', error);
    } finally {
        hideLoading();
    }
}

// Exibir registros na tabela
function displayRecords(records) {
    tableBody.innerHTML = '';
    
    if (records.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 2rem; color: #718096;">
                    Nenhum registro encontrado
                </td>
            </tr>
        `;
        return;
    }
    
    records.forEach(record => {
        const row = createTableRow(record);
        tableBody.appendChild(row);
    });
}

// Criar linha da tabela
function createTableRow(record) {
    const row = document.createElement('tr');
    
    const statusClass = record.status === 'concluido' ? 'status-concluido' : 'status-em-andamento';
    const statusText = record.status === 'concluido' ? 'Concluído' : 'Em Andamento';
    
    row.innerHTML = `
        <td>${record.empresa || ''}</td>
        <td>${record.ordemServico || ''}</td>
        <td>${record.solicitante || ''}</td>
        <td>${record.maquina || ''}</td>
        <td>${record.codigoChamado || ''}</td>
        <td>${formatDate(record.dataEntrada) || ''}</td>
        <td>${formatDate(record.dataSaida) || ''}</td>
        <td>
            <span class="status-badge ${statusClass}">${statusText}</span>
        </td>
        <td>
            <div class="table-actions">
                <button class="btn btn-warning btn-sm" onclick="editRecord(${record.id})">
                    <i class="fas fa-edit"></i> Editar
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteRecord(${record.id})">
                    <i class="fas fa-trash"></i> Excluir
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// Formatar data para exibição
function formatDate(dateString) {
    if (!dateString) return '';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    } catch (error) {
        return dateString;
    }
}

// Manipular envio do formulário
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Remover campo recordId se estiver vazio
    if (!data.recordId) {
        delete data.recordId;
    }
    
    try {
        showLoading();
        
        if (currentEditingId) {
            // Atualizar registro existente
            await apiRequest(`records/${currentEditingId}`, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
            showNotification('Registro atualizado com sucesso!', 'success');
        } else {
            // Criar novo registro
            await apiRequest('records', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            showNotification('Registro criado com sucesso!', 'success');
        }
        
        resetForm();
        loadRecords();
        
    } catch (error) {
        console.error('Erro ao salvar registro:', error);
    } finally {
        hideLoading();
    }
}

// Editar registro
async function editRecord(id) {
    try {
        showLoading();
        const record = await apiRequest(`records/${id}`);
        
        // Preencher formulário com dados do registro
        document.getElementById('recordId').value = record.id;
        document.getElementById('empresa').value = record.empresa || '';
        document.getElementById('ordemServico').value = record.ordemServico || '';
        document.getElementById('solicitante').value = record.solicitante || '';
        document.getElementById('maquina').value = record.maquina || '';
        document.getElementById('codigoChamado').value = record.codigoChamado || '';
        document.getElementById('dataEntrada').value = record.dataEntrada || '';
        document.getElementById('dataSaida').value = record.dataSaida || '';
        document.getElementById('descricaoServico').value = record.descricaoServico || '';
        
        currentEditingId = id;
        formTitle.textContent = 'Editar Registro';
        
        // Scroll para o formulário
        document.querySelector('.form-section').scrollIntoView({ behavior: 'smooth' });
        
    } catch (error) {
        console.error('Erro ao carregar registro para edição:', error);
    } finally {
        hideLoading();
    }
}

// Excluir registro
async function deleteRecord(id) {
    if (!confirm('Tem certeza que deseja excluir este registro?')) {
        return;
    }
    
    try {
        showLoading();
        await apiRequest(`records/${id}`, {
            method: 'DELETE'
        });
        
        showNotification('Registro excluído com sucesso!', 'success');
        loadRecords();
        
    } catch (error) {
        console.error('Erro ao excluir registro:', error);
    } finally {
        hideLoading();
    }
}

// Resetar formulário
function resetForm() {
    form.reset();
    currentEditingId = null;
    formTitle.textContent = 'Adicionar Novo Registro';
    document.getElementById('recordId').value = '';
}

// Buscar registros
async function handleSearch() {
    const searchTerm = searchInput.value.trim();
    
    if (searchTerm.length === 0) {
        loadRecords();
        return;
    }
    
    if (searchTerm.length < 2) {
        return;
    }
    
    try {
        showLoading();
        const response = await apiRequest(`search?q=${encodeURIComponent(searchTerm)}`);
        displayRecords(response.results);
        updateRecordCount(response.total);
    } catch (error) {
        console.error('Erro na busca:', error);
    } finally {
        hideLoading();
    }
}

// Função de busca (chamada pelo botão)
function searchRecords() {
    handleSearch();
}

// Filtrar registros por status
function filterRecords() {
    const filterValue = statusFilter.value;
    const rows = tableBody.querySelectorAll('tr');
    
    rows.forEach(row => {
        if (row.cells.length === 1) return; // Pular linha de "nenhum registro"
        
        const statusCell = row.cells[7];
        const statusBadge = statusCell.querySelector('.status-badge');
        
        if (!statusBadge) return;
        
        const recordStatus = statusBadge.classList.contains('status-concluido') ? 'concluido' : 'em_andamento';
        
        if (filterValue === '' || recordStatus === filterValue) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Atualizar contador
    const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none' && row.cells.length > 1);
    updateRecordCount(visibleRows.length);
}

// Atualizar contador de registros
function updateRecordCount(count) {
    recordCount.textContent = `${count} registro${count !== 1 ? 's' : ''}`;
}

// Mostrar notificação
function showNotification(message, type = 'info') {
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Adicionar estilos se não existirem
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideIn 0.3s ease;
            }
            .notification-success { background-color: #38a169; }
            .notification-error { background-color: #e53e3e; }
            .notification-info { background-color: #3182ce; }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Remover após 5 segundos
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Função debounce para otimizar busca
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
