<?php

require_once 'vendor/autoload.php';
require_once 'config/config.php';

use LabControl\ExcelManager;

echo "<h1>Teste do Sistema de Controle do Laboratório</h1>";

try {
    echo "<h2>1. Testando conexão com o arquivo Excel...</h2>";
    
    // Verificar se o arquivo existe
    if (file_exists(EXCEL_FILE_PATH)) {
        echo "✅ Arquivo Excel encontrado: " . EXCEL_FILE_PATH . "<br>";
    } else {
        echo "❌ Arquivo Excel não encontrado: " . EXCEL_FILE_PATH . "<br>";
        echo "📝 Criando arquivo de teste...<br>";
        
        // Criar diretório se não existir
        $dir = dirname(EXCEL_FILE_PATH);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }
    
    echo "<h2>2. Testando ExcelManager...</h2>";
    
    $excelManager = new ExcelManager(EXCEL_FILE_PATH);
    echo "✅ ExcelManager instanciado com sucesso<br>";
    
    echo "<h2>3. Testando operações CRUD...</h2>";
    
    // Teste de criação
    echo "<h3>3.1 Teste de Criação (CREATE)</h3>";
    $testData = [
        'empresa' => 'Empresa Teste',
        'ordemServico' => 'OS-001',
        'solicitante' => 'João Silva',
        'maquina' => 'Notebook Dell',
        'codigoChamado' => 'CH-001',
        'dataEntrada' => '2024-01-15',
        'dataSaida' => '',
        'descricaoServico' => 'Manutenção preventiva do notebook'
    ];
    
    $newId = $excelManager->addRecord($testData);
    echo "✅ Registro criado com ID: $newId<br>";
    
    // Teste de leitura
    echo "<h3>3.2 Teste de Leitura (READ)</h3>";
    $records = $excelManager->getAllRecords();
    echo "✅ Total de registros encontrados: " . count($records) . "<br>";
    
    if (count($records) > 0) {
        echo "📋 Último registro:<br>";
        $lastRecord = end($records);
        foreach ($lastRecord as $key => $value) {
            echo "- $key: $value<br>";
        }
    }
    
    // Teste de busca
    echo "<h3>3.3 Teste de Busca (SEARCH)</h3>";
    $searchResults = $excelManager->searchRecords('Teste');
    echo "✅ Registros encontrados na busca por 'Teste': " . count($searchResults) . "<br>";
    
    // Teste de atualização
    echo "<h3>3.4 Teste de Atualização (UPDATE)</h3>";
    if ($newId) {
        $updateData = [
            'dataSaida' => '2024-01-16',
            'descricaoServico' => 'Manutenção preventiva concluída'
        ];
        
        $excelManager->updateRecord($newId, $updateData);
        echo "✅ Registro $newId atualizado com sucesso<br>";
        
        // Verificar atualização
        $updatedRecord = $excelManager->getRecord($newId);
        if ($updatedRecord && $updatedRecord['dataSaida'] === '2024-01-16') {
            echo "✅ Atualização verificada com sucesso<br>";
        } else {
            echo "❌ Erro na verificação da atualização<br>";
        }
    }
    
    echo "<h2>4. Testando API...</h2>";
    
    // Testar se a API está acessível
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/records';
    echo "🔗 URL da API: $apiUrl<br>";
    
    // Verificar se mod_rewrite está funcionando
    if (file_exists('.htaccess')) {
        echo "✅ Arquivo .htaccess encontrado<br>";
    } else {
        echo "❌ Arquivo .htaccess não encontrado<br>";
    }
    
    echo "<h2>5. Verificações do Sistema</h2>";
    
    // Verificar PHP
    echo "🐘 Versão do PHP: " . PHP_VERSION . "<br>";
    
    // Verificar extensões necessárias
    $requiredExtensions = ['zip', 'xml', 'gd', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            echo "✅ Extensão $ext: carregada<br>";
        } else {
            echo "❌ Extensão $ext: não carregada<br>";
        }
    }
    
    // Verificar permissões
    if (is_writable(dirname(EXCEL_FILE_PATH))) {
        echo "✅ Diretório do Excel: gravável<br>";
    } else {
        echo "❌ Diretório do Excel: não gravável<br>";
    }
    
    echo "<h2>✅ Teste concluído com sucesso!</h2>";
    echo "<p><strong>O sistema está pronto para uso.</strong></p>";
    echo "<p><a href='index.html' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Acessar Sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erro durante o teste:</h2>";
    echo "<p style='color: red; background: #ffe6e6; padding: 10px; border-radius: 5px;'>" . $e->getMessage() . "</p>";
    echo "<h3>Possíveis soluções:</h3>";
    echo "<ul>";
    echo "<li>Verificar se o caminho do arquivo Excel está correto</li>";
    echo "<li>Verificar permissões de leitura/escrita no diretório</li>";
    echo "<li>Verificar se o XAMPP está rodando corretamente</li>";
    echo "<li>Verificar se as extensões PHP necessárias estão instaladas</li>";
    echo "</ul>";
}
?>
