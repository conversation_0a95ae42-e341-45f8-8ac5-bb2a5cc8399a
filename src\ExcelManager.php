<?php

namespace LabControl;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ExcelManager
{
    private $filePath;
    private $spreadsheet;
    private $worksheet;
    
    // Mapeamento das colunas
    private $columns = [
        'A' => 'empresa',
        'B' => 'ordemServico', 
        'C' => 'solicitante',
        'D' => 'maquina',
        'E' => 'codigoChamado',
        'F' => 'dataEntrada',
        'G' => 'dataSaida',
        'H' => 'descricaoServico'
    ];

    public function __construct($filePath)
    {
        $this->filePath = $filePath;
        $this->loadSpreadsheet();
    }

    private function loadSpreadsheet()
    {
        try {
            if (file_exists($this->filePath)) {
                $this->spreadsheet = IOFactory::load($this->filePath);
            } else {
                // Criar novo arquivo se não existir
                $this->createNewSpreadsheet();
            }
            
            $this->worksheet = $this->spreadsheet->getActiveSheet();
            
            // Verificar se tem cabeçalho, se não, criar
            if (empty($this->worksheet->getCell('A1')->getValue())) {
                $this->createHeaders();
            }
            
        } catch (\Exception $e) {
            throw new \Exception("Erro ao carregar planilha: " . $e->getMessage());
        }
    }

    private function createNewSpreadsheet()
    {
        $this->spreadsheet = new Spreadsheet();
        $this->worksheet = $this->spreadsheet->getActiveSheet();
        $this->createHeaders();
        $this->save();
    }

    private function createHeaders()
    {
        $headers = [
            'A1' => 'Empresa',
            'B1' => 'Ordem De Serviço', 
            'C1' => 'Solicitante',
            'D1' => 'Máquina',
            'E1' => 'Código do chamado',
            'F1' => 'Data de entrada',
            'G1' => 'Data de saida',
            'H1' => 'Descrição do serviço'
        ];

        foreach ($headers as $cell => $value) {
            $this->worksheet->setCellValue($cell, $value);
        }
    }

    public function getAllRecords()
    {
        $records = [];
        $highestRow = $this->worksheet->getHighestRow();
        
        for ($row = 2; $row <= $highestRow; $row++) {
            $record = [];
            $record['id'] = $row;
            
            foreach ($this->columns as $col => $field) {
                $cellValue = $this->worksheet->getCell($col . $row)->getValue();
                
                // Tratar datas
                if (in_array($field, ['dataEntrada', 'dataSaida']) && !empty($cellValue)) {
                    if (Date::isDateTime($this->worksheet->getCell($col . $row))) {
                        $record[$field] = Date::excelToDateTimeObject($cellValue)->format('Y-m-d');
                    } else {
                        $record[$field] = $cellValue;
                    }
                } else {
                    $record[$field] = $cellValue ?? '';
                }
            }
            
            // Determinar status
            $record['status'] = empty($record['dataSaida']) ? 'em_andamento' : 'concluido';
            
            // Só adicionar se pelo menos um campo não estiver vazio
            if (!empty(array_filter($record, function($value) { return !empty($value) && $value !== 'em_andamento' && $value !== 'concluido'; }))) {
                $records[] = $record;
            }
        }
        
        return $records;
    }

    public function getRecord($id)
    {
        $records = $this->getAllRecords();
        foreach ($records as $record) {
            if ($record['id'] == $id) {
                return $record;
            }
        }
        return null;
    }

    public function addRecord($data)
    {
        $highestRow = $this->worksheet->getHighestRow();
        $newRow = $highestRow + 1;
        
        foreach ($this->columns as $col => $field) {
            if (isset($data[$field])) {
                $value = $data[$field];
                
                // Tratar datas
                if (in_array($field, ['dataEntrada', 'dataSaida']) && !empty($value)) {
                    $dateTime = \DateTime::createFromFormat('Y-m-d', $value);
                    if ($dateTime) {
                        $value = Date::PHPToExcel($dateTime);
                        $this->worksheet->getStyle($col . $newRow)->getNumberFormat()
                            ->setFormatCode('dd/mm/yyyy');
                    }
                }
                
                $this->worksheet->setCellValue($col . $newRow, $value);
            }
        }
        
        $this->save();
        return $newRow;
    }

    public function updateRecord($id, $data)
    {
        $row = $id;
        
        foreach ($this->columns as $col => $field) {
            if (isset($data[$field])) {
                $value = $data[$field];
                
                // Tratar datas
                if (in_array($field, ['dataEntrada', 'dataSaida']) && !empty($value)) {
                    $dateTime = \DateTime::createFromFormat('Y-m-d', $value);
                    if ($dateTime) {
                        $value = Date::PHPToExcel($dateTime);
                        $this->worksheet->getStyle($col . $row)->getNumberFormat()
                            ->setFormatCode('dd/mm/yyyy');
                    }
                }
                
                $this->worksheet->setCellValue($col . $row, $value);
            }
        }
        
        $this->save();
        return true;
    }

    public function deleteRecord($id)
    {
        $row = $id;
        $this->worksheet->removeRow($row);
        $this->save();
        return true;
    }

    public function searchRecords($searchTerm)
    {
        $allRecords = $this->getAllRecords();
        $results = [];
        
        $searchTerm = strtolower($searchTerm);
        
        foreach ($allRecords as $record) {
            $searchableFields = ['empresa', 'solicitante', 'maquina', 'codigoChamado', 'descricaoServico'];
            
            foreach ($searchableFields as $field) {
                if (strpos(strtolower($record[$field]), $searchTerm) !== false) {
                    $results[] = $record;
                    break;
                }
            }
        }
        
        return $results;
    }

    private function save()
    {
        try {
            $writer = new Xlsx($this->spreadsheet);
            $writer->save($this->filePath);
        } catch (\Exception $e) {
            throw new \Exception("Erro ao salvar planilha: " . $e->getMessage());
        }
    }
}
?>
