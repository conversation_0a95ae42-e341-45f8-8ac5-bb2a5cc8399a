RewriteEngine On

# Redirecionar todas as requisições da API para api/index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Configurações de segurança
<Files "composer.json">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "composer.lock">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Directory "vendor">
    Order allow,deny
    Deny from all
</Directory>

<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

<Directory "src">
    Order allow,deny
    Deny from all
</Directory>

# Configurações de cache para assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
