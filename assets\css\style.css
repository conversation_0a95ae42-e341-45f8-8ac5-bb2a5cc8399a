/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header h1 i {
    font-size: 2.2rem;
}

/* Seçõ<PERSON> principais */
.form-section, .filters-section, .table-section {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.form-section h2, .table-section h2 {
    color: #4a5568;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Formulário */
.equipment-form {
    display: grid;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Botões */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background-color: #cbd5e0;
}

.btn-danger {
    background-color: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background-color: #c53030;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: #dd6b20;
    color: white;
}

.btn-warning:hover {
    background-color: #c05621;
    transform: translateY(-1px);
}

.btn-search {
    background-color: #38a169;
    color: white;
    padding: 12px 16px;
}

.btn-search:hover {
    background-color: #2f855a;
}

.btn-refresh {
    background-color: #3182ce;
    color: white;
}

.btn-refresh:hover {
    background-color: #2c5282;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
}

/* Ações do formulário */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 1rem;
}

/* Filtros */
.filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    gap: 0.5rem;
    flex: 1;
    min-width: 300px;
}

.search-group input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
}

.filter-group select {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    background-color: white;
    min-width: 200px;
}

/* Tabela */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.table-info {
    color: #718096;
    font-size: 0.9rem;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.records-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.records-table th {
    background-color: #f7fafc;
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
}

.records-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: top;
}

.records-table tbody tr:hover {
    background-color: #f7fafc;
}

/* Status badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-em-andamento {
    background-color: #fed7d7;
    color: #c53030;
}

.status-concluido {
    background-color: #c6f6d5;
    color: #2f855a;
}

/* Ações da tabela */
.table-actions {
    display: flex;
    gap: 0.5rem;
    white-space: nowrap;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: #4a5568;
    font-weight: 600;
}

/* Responsividade */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-group {
        min-width: auto;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .table-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .form-section, .filters-section, .table-section {
        padding: 1rem;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}
