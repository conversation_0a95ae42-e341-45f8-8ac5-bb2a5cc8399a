<?php

// Configurações do sistema
define('EXCEL_FILE_PATH', '\\\\172.20.0.252\\dados\\OneDrive\\1-SUPORTE\\Controles\\Lab\\Controle Entradas e Saidas.xlsx');

// Configurações de erro
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configurações de timezone
date_default_timezone_set('America/Sao_Paulo');

// Headers para API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Função para retornar resposta JSON
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

// Função para retornar erro JSON
function jsonError($message, $status = 400) {
    jsonResponse(['error' => $message], $status);
}

// Verificar se o arquivo Excel existe
function checkExcelFile() {
    if (!file_exists(EXCEL_FILE_PATH)) {
        jsonError('Arquivo Excel não encontrado: ' . EXCEL_FILE_PATH, 404);
    }
    
    if (!is_readable(EXCEL_FILE_PATH)) {
        jsonError('Arquivo Excel não pode ser lido. Verifique as permissões.', 403);
    }
}
?>
