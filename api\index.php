<?php

require_once '../vendor/autoload.php';
require_once '../config/config.php';

use LabControl\ExcelManager;

try {
    // Verificar se o arquivo Excel existe
    checkExcelFile();
    
    // Instanciar o gerenciador Excel
    $excelManager = new ExcelManager(EXCEL_FILE_PATH);
    
    // Obter método HTTP e rota
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Remover 'api' do caminho se presente
    if ($pathParts[0] === 'api') {
        array_shift($pathParts);
    }
    
    $endpoint = $pathParts[0] ?? '';
    $id = $pathParts[1] ?? null;
    
    // Roteamento
    switch ($endpoint) {
        case 'records':
            handleRecordsEndpoint($excelManager, $method, $id);
            break;
            
        case 'search':
            handleSearchEndpoint($excelManager, $method);
            break;
            
        default:
            jsonError('Endpoint não encontrado', 404);
    }
    
} catch (Exception $e) {
    jsonError('Erro interno: ' . $e->getMessage(), 500);
}

function handleRecordsEndpoint($excelManager, $method, $id) {
    switch ($method) {
        case 'GET':
            if ($id) {
                // Buscar registro específico
                $record = $excelManager->getRecord($id);
                if ($record) {
                    jsonResponse($record);
                } else {
                    jsonError('Registro não encontrado', 404);
                }
            } else {
                // Buscar todos os registros
                $records = $excelManager->getAllRecords();
                jsonResponse([
                    'records' => $records,
                    'total' => count($records)
                ]);
            }
            break;
            
        case 'POST':
            // Criar novo registro
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data) {
                jsonError('Dados inválidos', 400);
            }
            
            // Validar campos obrigatórios
            $requiredFields = ['empresa', 'ordemServico', 'solicitante', 'maquina', 'codigoChamado', 'dataEntrada', 'descricaoServico'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    jsonError("Campo obrigatório: $field", 400);
                }
            }
            
            try {
                $newId = $excelManager->addRecord($data);
                jsonResponse([
                    'message' => 'Registro criado com sucesso',
                    'id' => $newId
                ], 201);
            } catch (Exception $e) {
                jsonError('Erro ao criar registro: ' . $e->getMessage(), 500);
            }
            break;
            
        case 'PUT':
            // Atualizar registro
            if (!$id) {
                jsonError('ID do registro é obrigatório', 400);
            }
            
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data) {
                jsonError('Dados inválidos', 400);
            }
            
            // Verificar se o registro existe
            $existingRecord = $excelManager->getRecord($id);
            if (!$existingRecord) {
                jsonError('Registro não encontrado', 404);
            }
            
            try {
                $excelManager->updateRecord($id, $data);
                jsonResponse(['message' => 'Registro atualizado com sucesso']);
            } catch (Exception $e) {
                jsonError('Erro ao atualizar registro: ' . $e->getMessage(), 500);
            }
            break;
            
        case 'DELETE':
            // Deletar registro
            if (!$id) {
                jsonError('ID do registro é obrigatório', 400);
            }
            
            // Verificar se o registro existe
            $existingRecord = $excelManager->getRecord($id);
            if (!$existingRecord) {
                jsonError('Registro não encontrado', 404);
            }
            
            try {
                $excelManager->deleteRecord($id);
                jsonResponse(['message' => 'Registro deletado com sucesso']);
            } catch (Exception $e) {
                jsonError('Erro ao deletar registro: ' . $e->getMessage(), 500);
            }
            break;
            
        default:
            jsonError('Método não permitido', 405);
    }
}

function handleSearchEndpoint($excelManager, $method) {
    if ($method !== 'GET') {
        jsonError('Método não permitido', 405);
    }
    
    $searchTerm = $_GET['q'] ?? '';
    
    if (empty($searchTerm)) {
        jsonError('Termo de busca é obrigatório', 400);
    }
    
    try {
        $results = $excelManager->searchRecords($searchTerm);
        jsonResponse([
            'results' => $results,
            'total' => count($results),
            'search_term' => $searchTerm
        ]);
    } catch (Exception $e) {
        jsonError('Erro na busca: ' . $e->getMessage(), 500);
    }
}
?>
