<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Controle de Entrada e Saída - Laboratório</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-laptop"></i> Controle de Entrada e Saída - Laboratório</h1>
        </header>

        <main class="main-content">
            <!-- Formulário para adicionar/editar registros -->
            <section class="form-section">
                <h2 id="form-title">Adicionar Novo Registro</h2>
                <form id="equipmentForm" class="equipment-form">
                    <input type="hidden" id="recordId" name="recordId">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="empresa">Empresa:</label>
                            <input type="text" id="empresa" name="empresa" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="ordemServico">Ordem de Serviço:</label>
                            <input type="text" id="ordemServico" name="ordemServico" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="solicitante">Solicitante:</label>
                            <input type="text" id="solicitante" name="solicitante" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="maquina">Máquina:</label>
                            <input type="text" id="maquina" name="maquina" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="codigoChamado">Código do Chamado:</label>
                            <input type="text" id="codigoChamado" name="codigoChamado" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="dataEntrada">Data de Entrada:</label>
                            <input type="date" id="dataEntrada" name="dataEntrada" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="dataSaida">Data de Saída:</label>
                            <input type="date" id="dataSaida" name="dataSaida">
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="descricaoServico">Descrição do Serviço:</label>
                        <textarea id="descricaoServico" name="descricaoServico" rows="3" required></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-times"></i> Cancelar
                        </button>
                    </div>
                </form>
            </section>

            <!-- Filtros e busca -->
            <section class="filters-section">
                <div class="filters">
                    <div class="search-group">
                        <input type="text" id="searchInput" placeholder="Buscar por empresa, solicitante, máquina...">
                        <button onclick="searchRecords()" class="btn btn-search">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="filter-group">
                        <select id="statusFilter">
                            <option value="">Todos os Status</option>
                            <option value="em_andamento">Em Andamento</option>
                            <option value="concluido">Concluído</option>
                        </select>
                    </div>
                    
                    <button onclick="loadRecords()" class="btn btn-refresh">
                        <i class="fas fa-sync-alt"></i> Atualizar
                    </button>
                </div>
            </section>

            <!-- Tabela de registros -->
            <section class="table-section">
                <div class="table-header">
                    <h2>Registros do Laboratório</h2>
                    <div class="table-info">
                        <span id="recordCount">0 registros</span>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="recordsTable" class="records-table">
                        <thead>
                            <tr>
                                <th>Empresa</th>
                                <th>OS</th>
                                <th>Solicitante</th>
                                <th>Máquina</th>
                                <th>Código</th>
                                <th>Entrada</th>
                                <th>Saída</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Carregando...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/app.js"></script>
</body>
</html>
